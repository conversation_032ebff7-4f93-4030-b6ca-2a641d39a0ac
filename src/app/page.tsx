"use client";

import { type JSX } from "react";
import { cn } from "@/lib/utils";
import HeroSection from "@/components/home/<USER>";
import SearchSection from "@/components/home/<USER>";
import DestinationsSection from "@/components/home/<USER>";
import PackagesSection from "@/components/home/<USER>";
import CarCharterSection from "@/components/home/<USER>";
import WhyChooseUsSection from "@/components/home/<USER>";
import ContactSection from "@/components/home/<USER>";
import BlogSection from "@/components/home/<USER>";
import InteractiveMapSection from "@/components/home/<USER>";
import AnimatedSection from "@/components/home/<USER>";
import { motion, AnimatePresence, useReducedMotion } from "framer-motion";

// const bgColors = [
//     cn("bg-[#368d1b]"),
//     cn("bg-[#bc5555]"),
//     cn("bg-[#4b8d50]"),
//     cn("bg-[#b9b225]"),
//     cn("bg-[#065f35]"),
//     cn("bg-[#6f0b6f]"),
//     cn("bg-[#4d031c]"),
//     cn("bg-[#123fb1]"),
// ] as const;

const pageSections = [
    { name: "SearchSection", component: SearchSection },
    { name: "CarCharterSection", component: CarCharterSection },
    { name: "DestinationsSection", component: DestinationsSection },
    { name: "InteractiveMapSection", component: InteractiveMapSection },
    { name: "PackagesSection", component: PackagesSection },
    { name: "BlogSection", component: BlogSection },
    { name: "WhyChooseUsSection", component: WhyChooseUsSection },
    { name: "ContactSection", component: ContactSection },
];

const variants = {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
};

const notAnimate = { opacity: 1, y: 0 };

const Home = (): JSX.Element => {
    const reducedMotion = useReducedMotion();
    return (
        <AnimatePresence onExitComplete={() => window.scrollTo(0, 0)}>
            <motion.main
                initial={reducedMotion ? notAnimate : "initial"}
                animate={reducedMotion ? notAnimate : "animate"}
                exit={reducedMotion ? notAnimate : "exit"}
                variants={variants}
                transition={
                    reducedMotion
                        ? { duration: 0 }
                        : { duration: 0.2, ease: "easeOut" }
                }
            >
                <HeroSection />
                {pageSections.map((Section) => (
                    <AnimatedSection
                        key={Section.name}
                        className={cn(
                            "relative",
                            "overflow-hidden",
                            "py-6 md:py-10",
                            "w-full",
                            // bgColors[
                            //     pageSections.indexOf(Section) % bgColors.length
                            // ],
                        )}
                    >
                        <Section.component />
                    </AnimatedSection>
                ))}
            </motion.main>
        </AnimatePresence>
    );
};

export default Home;
