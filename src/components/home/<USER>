"use client";

import { useEffect, useState } from "react";
import SectionC<PERSON>, { CardData, ButtonFunc } from "./SectionCard";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import BaliMapDark from "@/components/svg/BaliMapDark";
import BaliMapLight from "@/components/svg/BaliMapLight";
import { useTheme } from "next-themes";

// Animation configuration constants
const ANIMATION_CONFIG = {
    cardTransition: {
        duration: 0.5,
        ease: "easeInOut",
    },
    cardVariants: {
        initial: { opacity: 0, y: 10 },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -10 },
    },
    pinTransition: {
        duration: 300, // in milliseconds for CSS transitions
    },
} as const;

// Styling constants
const STYLING_CONFIG = {
    pin: {
        base: cn(
            "block h-3 w-3 rounded-full bg-accent ring-accent/50",
            "transition-transform duration-300",
        ),
        active: "scale-150 ring-2",
        inactive: "scale-100 group-hover:scale-125",
        tooltip: cn(
            "absolute left-1/2 -translate-x-1/2 -bottom-6",
            "whitespace-nowrap rounded bg-primary px-2 py-1",
            "text-xs text-primary-foreground opacity-0 shadow-lg",
            "transition-opacity duration-300 pointer-events-none",
        ),
        tooltipActive: "opacity-100",
    },
} as const;

// The Location type is compatible with GenericCardData
type Location = CardData & {
    x: string;
    y: string;
};

const mapPins: Location[] = [
    {
        name: "Ubud",
        description:
            "The cultural heart of Bali, known for its lush rice paddies, art galleries, and spiritual retreats.",
        image: "https://placehold.co/600x400.png",
        aiHint: "bali ubud",
        x: "66%",
        y: "55%",
        link: "/#",
    },
    {
        name: "Canggu",
        description:
            "A vibrant coastal town with a laid-back surf culture, trendy cafes, and lively beach clubs.",
        image: "https://placehold.co/600x400.png",
        aiHint: "bali canggu",
        x: "56%",
        y: "77%",
        link: "#",
    },
    {
        name: "Kintamani",
        description:
            "Home to the majestic Mount Batur volcano and a stunning caldera lake, offering breathtaking views.",
        image: "https://placehold.co/600x400.png",
        aiHint: "mount batur",
        x: "70%",
        y: "20%",
        link: "/#",
    },
    {
        name: "Nusa Penida",
        description:
            "A rugged island paradise offering dramatic cliffs, pristine beaches, and incredible diving spots.",
        image: "https://placehold.co/600x400.png",
        aiHint: "nusa penida",
        x: "85%",
        y: "84%",
        link: "/#",
    },
    {
        name: "Uluwatu",
        description:
            "Famous for its cliff-top temple, stunning sunsets, and world-class surf breaks.",
        image: "https://placehold.co/600x400.png",
        aiHint: "uluwatu temple",
        x: "52%",
        y: "97%",
        link: "/#",
    },
    {
        name: "Seminyak",
        description:
            "Bali's hub for luxury resorts, high-end shopping, and world-class dining experiences.",
        image: "https://placehold.co/600x400.png",
        aiHint: "bali seminyak",
        x: "57%",
        y: "82%",
        link: "/#",
    },
    {
        name: "Amed",
        description:
            "A string of quiet fishing villages in East Bali, known for black sand beaches and spectacular diving.",
        image: "https://placehold.co/600x400.png",
        aiHint: "amed beach",
        x: "93%",
        y: "32%",
        link: "/#",
    },
    {
        name: "Lovina",
        description:
            "Located on the North coast, Lovina is famous for its black sand beaches and early morning dolphin tours.",
        image: "https://placehold.co/600x400.png",
        aiHint: "bali dolphins",
        x: "50%",
        y: "8%",
        link: "/#",
    },
    {
        name: "Pemuteran",
        description:
            "A small, laid-back village in Northwest Bali, perfect for diving, snorkeling, and relaxation away from the crowds.",
        image: "https://placehold.co/600x400.png",
        aiHint: "bali snorkeling",
        x: "20%",
        y: "12%",
        link: "/#",
    },
    {
        name: "Jatiluwih",
        description:
            "Famous for its dramatic and exotic landscapes. The Jatiluwih rice terraces are a UNESCO Cultural Heritage Site.",
        image: "/images/bali_map/jatiluwih-rice-terrace.webp",
        imageClass: cn("object-[center_-1px] md:object-[center_-27px]"),
        aiHint: "jatiluwih rice terraces",
        x: "53%",
        y: "45%",
        link: "/#",
    },
    {
        name: "Bedugul (Lake Beratan)",
        description:
            "Home to the iconic Ulun Danu Beratan Temple, this highland area offers cool weather and stunning lake views.",
        image: "/images/bali_map/beratan_temple.webp",
        imageClass: "object-[center_-21px] md:object-[center_-23px]", // 23px from top, object-[75%_center] from left
        aiHint: "beratan temple bali",
        x: "58%",
        y: "30%",
        link: "/#",
    },
    {
        name: "Munduk",
        description:
            "A charming mountain village known for its scenic treks, stunning waterfalls, and coffee plantations.",
        image: "https://placehold.co/600x400.png",
        aiHint: "munduk bali",
        x: "49%",
        y: "29%",
        link: "/#",
    },
    {
        name: "West Bali National Park",
        description:
            "A conservation area featuring diverse ecosystems, from rainforests to coral reefs, home to the rare Bali Starling.",
        image: "https://placehold.co/600x400.png",
        aiHint: "west bali park",
        x: "3%",
        y: "8%",
        link: "/#",
    },
    {
        name: "Medewi Beach",
        description:
            "A quiet, black-sand beach on the west coast, famous for its long, gentle left-hand wave, perfect for longboarding.",
        image: "https://placehold.co/600x400.png",
        aiHint: "medewi beach surf",
        x: "35%",
        y: "48%",
        link: "/#",
    },
    {
        name: "Sekumpul Waterfall",
        description:
            "Often called the most beautiful waterfall in Bali, Sekumpul is a collection of seven stunning cascades in a lush jungle valley.",
        image: "https://placehold.co/600x400.png",
        aiHint: "sekumpul waterfall",
        x: "59%",
        y: "10%",
        link: "/#",
    },
    {
        name: "Denpasar",
        description:
            "Bali's bustling capital city, a center of commerce and home to historical sites and vibrant local markets.",
        image: "https://placehold.co/600x400.png",
        aiHint: "denpasar bali",
        x: "62%",
        y: "78%",
        link: "/#",
    },
    {
        name: "Candidasa",
        description:
            "A quiet coastal town in East Bali, offering a tranquil escape with beautiful beaches and a laid-back atmosphere.",
        image: "https://placehold.co/600x400.png",
        aiHint: "candidasa beach",
        x: "86%",
        y: "56%",
        link: "/#",
    },
] as Location[];

/**
 * Generates a contact message URL for a specific location
 */
const generateContactMessage = (locationName: string): string => {
    return `/#contact?message=I'm interested in visiting ${locationName}.`;
};

/**
 * Handles location pin click events
 */
const handleLocationClick = (
    location: Location,
    setActiveLocation: (location: Location) => void,
) => {
    setActiveLocation(location);
};

/**
 * Interactive pin component for map locations
 * Displays a clickable pin with hover effects and tooltip
 */
const LocationPin = ({
    location,
    isActive,
    onClick,
}: {
    location: Location;
    isActive: boolean;
    onClick: (location: Location) => void;
}) => (
    <button
        type="button"
        className="absolute -translate-x-1/2 -translate-y-1/2 group pointer-events-auto"
        style={{ left: location.x, top: location.y }}
        onClick={() => handleLocationClick(location, onClick)}
        aria-label={`Show details for ${location.name}`}
    >
        <span
            className={cn(
                STYLING_CONFIG.pin.base,
                isActive
                    ? STYLING_CONFIG.pin.active
                    : STYLING_CONFIG.pin.inactive,
            )}
        ></span>
        <span
            className={cn(
                STYLING_CONFIG.pin.tooltip,
                isActive ? STYLING_CONFIG.pin.tooltipActive : "",
            )}
        >
            {location.name}
        </span>
    </button>
);

const SectionTitle = ({ isMobile = false }: { isMobile?: boolean }) => {
    return (
        <div className={cn("text-center")}>
            <h2
                className={cn(
                    "font-bold tracking-normal font-headline text-special-card-fg",
                    isMobile ? "text-3xl" : "text-5xl",
                )}
            >
                Explore the Island
            </h2>
            <p
                className={cn(
                    "mx-auto max-w-3xl text-muted-foreground",
                    "md:text-xl/relaxed",
                    "mt-2 md:mt-4",
                )}
            >
                Click on the pins to discover more about Bali&#39;s most popular
                destinations.
            </p>
        </div>
    );
};

const MapSectionCard = ({
    activeLocation,
    isMobile,
}: {
    activeLocation: Location;
    isMobile: boolean;
}) => {
    return (
        <>
            <AnimatePresence mode="wait">
                <motion.div
                    key={activeLocation.name}
                    initial={ANIMATION_CONFIG.cardVariants.initial}
                    animate={ANIMATION_CONFIG.cardVariants.animate}
                    exit={ANIMATION_CONFIG.cardVariants.exit}
                    transition={ANIMATION_CONFIG.cardTransition}
                    className={cn("shadow-xl")}
                >
                    <SectionCard
                        data={activeLocation}
                        buttonText="Plan a Trip Here"
                        buttonLink={generateContactMessage(activeLocation.name)}
                        className={cn(
                            isMobile ? "max-w-sm sm:max-w-sm" : "md:max-w-sm",
                        )}
                        baliMap={true}
                    />
                </motion.div>
            </AnimatePresence>
        </>
    );
};

const SectionButton = ({ isMobile = false }: { isMobile?: boolean }) => {
    return (
        <ButtonFunc
            className={cn(isMobile ? "-mt-2" : "left-0 translate-x-0")}
            text="Explore All Destinations"
            link="#destinations"
            ariaLabel="Explore all destinations"
        />
    );
};

const InteractiveMapSection = () => {
    const [activeLocation, setActiveLocation] = useState<Location>(mapPins[0]);
    const [isMounted, setIsMounted] = useState(false);
    const { resolvedTheme } = useTheme();
    const isDark = resolvedTheme === "dark";

    // Prevent hydration mismatch by waiting for client-side mount
    useEffect(() => {
        setIsMounted(true);
    }, []);

    const MapAndCard = ({
        divClass,
        mapConfig,
        cardConfig,
        isMobile = false,
    }: {
        divClass: string;
        mapConfig: {
            divClass: string;
            mapClass?: string;
        };
        cardConfig?: {
            divClass: string;
        };
        isMobile?: boolean;
    }) => {
        if (isMounted) {
            const BaliMap = isDark ? BaliMapDark : BaliMapLight;
            return (
                <div className={cn("flex items-center", divClass)}>
                    <div
                        className={cn(
                            mapConfig.divClass,
                            "pointer-events-none",
                        )}
                    >
                        <BaliMap
                            className={cn(mapConfig.mapClass)}
                            style={{
                                filter: isDark
                                    ? "brightness(1.2) contrast(1.2) drop-shadow(1px 5px 2px #1f1f1fb4)"
                                    : "brightness(1.7) drop-shadow(2px 3px 2px #1f1f1fa0)",
                            }}
                        />
                        {mapPins.map((loc, index) => (
                            <LocationPin
                                key={index}
                                location={loc}
                                isActive={activeLocation.name === loc.name}
                                onClick={setActiveLocation}
                            />
                        ))}
                    </div>
                    <div className={cn(cardConfig?.divClass)}>
                        <MapSectionCard
                            activeLocation={activeLocation}
                            isMobile={isMobile}
                        />
                    </div>
                </div>
            );
        } else {
            return null;
        }
    };

    return (
        <section id="map">
            {/* Desktop view */}
            <div className={cn("hidden md:block space-y-12")}>
                <SectionTitle />
                <MapAndCard
                    divClass={cn(
                        "flex-row-reverse items-center justify-evenly",
                    )}
                    mapConfig={{
                        divClass: cn("relative group scale-[1.4]"),
                    }}
                    cardConfig={{
                        divClass: cn("h-fit scale-[.9]"),
                    }}
                />
                <SectionButton />
            </div>

            {/* Mobile view */}
            <div className={cn("block md:hidden")}>
                <SectionTitle />

                <MapAndCard
                    divClass={cn("flex-col")}
                    mapConfig={{
                        divClass: cn("relative group scale-[.85] mt-1"),
                    }}
                    cardConfig={{
                        divClass: cn("h-fit scale-[.8] -mt-9"),
                    }}
                    isMobile={true}
                />

                <SectionButton isMobile={true} />
            </div>
        </section>
    );
};

export default InteractiveMapSection;
